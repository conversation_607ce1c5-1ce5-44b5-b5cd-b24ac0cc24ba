#!/usr/bin/env python3
"""
配置文件 - 管理AugmentCode-Free的所有配置项
将硬编码值集中管理，提高代码可维护性
"""

# === 应用程序信息 ===
APP_NAME = "AugmentCode-Free"
APP_VERSION = "0.0.3"
APP_DESCRIPTION = "VS Code维护工具，用于清理数据库和修改遥测ID"

# === 文件大小限制 ===
MAX_FILE_SIZE_MB = 100  # 最大处理文件大小（MB）
MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024

# === 关键字验证 ===
KEYWORD_MIN_LENGTH = 1
KEYWORD_MAX_LENGTH = 100
KEYWORD_PATTERN = r'^[a-zA-Z0-9._-]+$'  # 允许的字符模式

# === 数据库配置 ===
DB_CONNECTION_TIMEOUT = 30.0  # 数据库连接超时（秒）
DB_ISOLATION_LEVEL = 'DEFERRED'  # 数据库隔离级别

# === 进程管理 ===
PROCESS_CLOSE_TIMEOUT = 15  # 进程关闭超时（秒）
GRACEFUL_SHUTDOWN_WAIT = 2  # 优雅关闭等待时间（秒）

# === VS Code 进程名称 ===
VSCODE_PROCESSES = {
    'windows': ['Code.exe', 'Code - Insiders.exe', 'Code - OSS.exe'],
    'darwin': ['Visual Studio Code', 'Visual Studio Code - Insiders'],
    'linux': ['code', 'code-insiders']
}

# === GUI 配置 ===
GUI_CONFIG = {
    'window_size': '380x580',
    'window_resizable': False,
    'background_color': '#f5f5f5',
    'animation_interval': 100,  # 毫秒
    'status_message_duration': 3000,  # 状态消息显示时间（毫秒）
    'message_queue_check_interval': 100,  # 消息队列检查间隔（毫秒）
}

# === 按钮样式 ===
BUTTON_STYLES = {
    'primary': {
        'normal_bg': '#4f46e5',
        'normal_fg': '#ffffff',
        'hover_bg': '#4338ca',
        'hover_fg': '#ffffff',
    },
    'secondary': {
        'normal_bg': '#f3f4f6',
        'normal_fg': '#6b7280',
        'hover_bg': '#e5e7eb',
        'hover_fg': '#374151',
    },
    'disabled': {
        'bg': '#d1d5db',
        'fg': '#9ca3af',
    }
}

# === 颜色配置 ===
COLORS = {
    'success': '#059669',
    'error': '#dc2626',
    'warning': '#d97706',
    'info': '#0ea5e9',
    'text_primary': '#1f2937',
    'text_secondary': '#4b5563',
    'text_muted': '#6b7280',
    'background': '#f5f5f5',
}

# === 字体配置 ===
FONTS = {
    'default': ('Microsoft YaHei', 10),
    'title': ('Microsoft YaHei', 18, 'bold'),
    'subtitle': ('Microsoft YaHei', 14, 'bold'),
    'button': ('Microsoft YaHei', 13),
    'small': ('Microsoft YaHei', 9),
    'console': ('Consolas', 9),
}

# === 日志配置 ===
LOG_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(levelname)s - %(message)s',
    'max_log_size_mb': 10,
    'backup_count': 3,
}

# === 安全配置 ===
SECURITY_CONFIG = {
    'enable_file_validation': True,
    'enable_keyword_validation': True,
    'enable_permission_check': True,
    'enable_size_check': True,
    'enable_backup_creation': True,
    'max_backup_files': 5,
}

# === 默认关键字 ===
DEFAULT_CLEANUP_KEYWORD = "augment"

# === 路径配置 ===
PATH_CONFIG = {
    'backup_suffix': '.backup',
    'temp_suffix': '.tmp',
    'log_directory': 'logs',
}

# === 错误消息 ===
ERROR_MESSAGES = {
    'invalid_keyword': "关键字无效。关键字必须是1-100个字符，只能包含字母、数字、点、连字符和下划线。",
    'file_not_found': "文件未找到",
    'permission_denied': "权限不足",
    'file_too_large': "文件过大",
    'database_locked': "数据库文件被锁定。请确保VS Code已完全关闭。",
    'vscode_running': "检测到VS Code正在运行！请先关闭VS Code再进行操作。",
    'backup_failed': "备份创建失败",
    'operation_cancelled': "操作已取消",
}

# === 成功消息 ===
SUCCESS_MESSAGES = {
    'backup_created': "备份创建成功",
    'database_cleaned': "数据库清理完成",
    'telemetry_modified': "遥测ID修改完成",
    'vscode_closed': "VS Code已关闭",
    'validation_passed': "验证通过",
}

# === 警告消息 ===
WARNING_MESSAGES = {
    'vscode_not_running': "VS Code未运行",
    'no_entries_found': "未找到匹配的条目",
    'partial_success': "部分操作成功",
    'graceful_close_failed': "优雅关闭失败，使用强制关闭",
}

# === 确认对话框消息 ===
DIALOG_MESSAGES = {
    'close_vscode_confirm': {
        'title': "关闭VSCode确认",
        'message': "• 若有未保存的内容请先进行保存\n"
                  "• Augment中需要备份的聊天记录请先备份\n\n"
                  "确认无误后才能关闭VSCode。\n\n"
                  "是否继续关闭VSCode？"
    },
    'run_all_confirm': {
        'title': "一键修改确认",
        'message': "此按钮会关闭VSCode并清除Augment聊天数据！\n\n"
                  "请确保：\n"
                  "• 文件已保存\n"
                  "• Augment中的重要聊天记录已备份\n\n"
                  "是否继续执行一键修改？"
    }
}

def get_vscode_processes(system_name: str) -> list:
    """
    根据操作系统获取VS Code进程名称列表
    
    Args:
        system_name: 操作系统名称 ('windows', 'darwin', 'linux')
        
    Returns:
        进程名称列表
    """
    return VSCODE_PROCESSES.get(system_name.lower(), [])

def get_color(color_name: str) -> str:
    """
    获取颜色值
    
    Args:
        color_name: 颜色名称
        
    Returns:
        颜色值（十六进制）
    """
    return COLORS.get(color_name, '#000000')

def get_font(font_name: str) -> tuple:
    """
    获取字体配置
    
    Args:
        font_name: 字体名称
        
    Returns:
        字体配置元组
    """
    return FONTS.get(font_name, FONTS['default'])

def get_button_style(style_name: str) -> dict:
    """
    获取按钮样式配置
    
    Args:
        style_name: 样式名称
        
    Returns:
        样式配置字典
    """
    return BUTTON_STYLES.get(style_name, BUTTON_STYLES['primary'])
