# 分析二进制文件中的字符串
param(
    [string]$FilePath = "augment-cleaner-windows-amd64-v3.2.exe"
)

Write-Host "🔍 分析Augment清理工具二进制文件..." -ForegroundColor Green
Write-Host "文件: $FilePath" -ForegroundColor Yellow

if (-not (Test-Path $FilePath)) {
    Write-Host "❌ 文件不存在: $FilePath" -ForegroundColor Red
    exit 1
}

# 获取文件信息
$fileInfo = Get-Item $FilePath
Write-Host "📊 文件大小: $([math]::Round($fileInfo.Length / 1MB, 2)) MB" -ForegroundColor Cyan

# 读取二进制文件并提取可打印字符串
Write-Host "`n🔍 搜索VS Code相关字符串..." -ForegroundColor Green

try {
    $bytes = [System.IO.File]::ReadAllBytes($FilePath)
    $content = [System.Text.Encoding]::ASCII.GetString($bytes)
    
    # 提取可打印字符串 (长度>=4的字符串)
    $strings = [regex]::Matches($content, '[\x20-\x7E]{4,}') | ForEach-Object { $_.Value }
    
    Write-Host "📋 总共找到 $($strings.Count) 个字符串" -ForegroundColor Cyan
    
    # 搜索VS Code相关字符串
    $vscodeStrings = $strings | Where-Object { 
        $_ -match "(?i)(vscode|visual.*studio.*code|code|\.vscode|extensions)" 
    }
    
    if ($vscodeStrings.Count -gt 0) {
        Write-Host "`n✅ 找到 $($vscodeStrings.Count) 个VS Code相关字符串:" -ForegroundColor Green
        $vscodeStrings | ForEach-Object { Write-Host "  • $_" -ForegroundColor White }
    } else {
        Write-Host "`n❌ 未找到VS Code相关字符串" -ForegroundColor Red
    }
    
    # 搜索Augment相关字符串
    Write-Host "`n🔍 搜索Augment相关字符串..." -ForegroundColor Green
    $augmentStrings = $strings | Where-Object { 
        $_ -match "(?i)(augment|\.augment|augment.*plugin|augment.*extension)" 
    } | Select-Object -First 20
    
    if ($augmentStrings.Count -gt 0) {
        Write-Host "`n✅ 找到 $($augmentStrings.Count) 个Augment相关字符串:" -ForegroundColor Green
        $augmentStrings | ForEach-Object { Write-Host "  • $_" -ForegroundColor White }
    }
    
    # 搜索路径相关字符串
    Write-Host "`n🔍 搜索路径相关字符串..." -ForegroundColor Green
    $pathStrings = $strings | Where-Object { 
        $_ -match "(?i)(appdata|localappdata|\.config|\.cache|extensions|plugins)" 
    } | Select-Object -First 15
    
    if ($pathStrings.Count -gt 0) {
        Write-Host "`n✅ 找到 $($pathStrings.Count) 个路径相关字符串:" -ForegroundColor Green
        $pathStrings | ForEach-Object { Write-Host "  • $_" -ForegroundColor White }
    }
    
    # 搜索清理相关字符串
    Write-Host "`n🔍 搜索清理功能相关字符串..." -ForegroundColor Green
    $cleanStrings = $strings | Where-Object { 
        $_ -match "(?i)(clean|clear|delete|remove|purge|reset)" 
    } | Select-Object -First 10
    
    if ($cleanStrings.Count -gt 0) {
        Write-Host "`n✅ 找到 $($cleanStrings.Count) 个清理功能相关字符串:" -ForegroundColor Green
        $cleanStrings | ForEach-Object { Write-Host "  • $_" -ForegroundColor White }
    }
    
} catch {
    Write-Host "❌ 分析失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎯 分析完成!" -ForegroundColor Green
