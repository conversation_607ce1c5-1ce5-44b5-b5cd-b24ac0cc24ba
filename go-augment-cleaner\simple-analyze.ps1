# Simple binary analysis
$FilePath = "augment-cleaner-windows-amd64-v3.2.exe"

Write-Host "Analyzing Augment Cleaner binary file..."
Write-Host "File: $FilePath"

if (-not (Test-Path $FilePath)) {
    Write-Host "File not found: $FilePath"
    exit 1
}

# Get file info
$fileInfo = Get-Item $FilePath
Write-Host "File size: $([math]::Round($fileInfo.Length / 1MB, 2)) MB"

# Read binary and extract strings
try {
    $bytes = [System.IO.File]::ReadAllBytes($FilePath)
    $content = [System.Text.Encoding]::ASCII.GetString($bytes)
    
    # Extract printable strings (length >= 4)
    $strings = [regex]::Matches($content, '[\x20-\x7E]{4,}') | ForEach-Object { $_.Value }
    
    Write-Host "Total strings found: $($strings.Count)"
    
    # Search for VS Code related strings
    $vscodeStrings = $strings | Where-Object { 
        $_ -match "(?i)(vscode|visual.*studio.*code|code|\.vscode|extensions)" 
    }
    
    Write-Host "`nVS Code related strings found: $($vscodeStrings.Count)"
    if ($vscodeStrings.Count -gt 0) {
        $vscodeStrings | Select-Object -First 10 | ForEach-Object { Write-Host "  - $_" }
    }
    
    # Search for Augment related strings
    $augmentStrings = $strings | Where-Object { 
        $_ -match "(?i)(augment)" 
    }
    
    Write-Host "`nAugment related strings found: $($augmentStrings.Count)"
    if ($augmentStrings.Count -gt 0) {
        $augmentStrings | Select-Object -First 10 | ForEach-Object { Write-Host "  - $_" }
    }
    
    # Search for path related strings
    $pathStrings = $strings | Where-Object { 
        $_ -match "(?i)(appdata|localappdata|\.config|\.cache|extensions|plugins)" 
    }
    
    Write-Host "`nPath related strings found: $($pathStrings.Count)"
    if ($pathStrings.Count -gt 0) {
        $pathStrings | Select-Object -First 10 | ForEach-Object { Write-Host "  - $_" }
    }
    
} catch {
    Write-Host "Analysis failed: $($_.Exception.Message)"
}

Write-Host "`nAnalysis complete!"
