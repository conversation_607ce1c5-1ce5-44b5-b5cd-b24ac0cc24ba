# 深度分析go-augment-cleaner的清理逻辑
$FilePath = "augment-cleaner-windows-amd64-v3.2.exe"

Write-Host "=== 深度清理逻辑分析 ===" -ForegroundColor Green

try {
    $bytes = [System.IO.File]::ReadAllBytes($FilePath)
    $content = [System.Text.Encoding]::ASCII.GetString($bytes)
    $strings = [regex]::Matches($content, '[\x20-\x7E]{4,}') | ForEach-Object { $_.Value }
    
    Write-Host "`n🔍 清理函数分析:" -ForegroundColor Yellow
    $cleanFunctions = $strings | Where-Object { 
        $_ -match "(?i)(clean|clear|delete|remove|purge)" 
    } | Where-Object { $_.Length -gt 8 } | Select-Object -First 15
    
    $cleanFunctions | ForEach-Object { Write-Host "  • $_" -ForegroundColor White }
    
    Write-Host "`n📁 路径模式分析:" -ForegroundColor Yellow
    $pathPatterns = $strings | Where-Object { 
        $_ -match "(?i)(appdata|localappdata|userprofile|\.vscode|\.config|extensions|globalStorage|workspaceStorage)" 
    } | Select-Object -First 15
    
    $pathPatterns | ForEach-Object { Write-Host "  • $_" -ForegroundColor White }
    
    Write-Host "`n🗃️ 数据库操作分析:" -ForegroundColor Yellow
    $dbOperations = $strings | Where-Object { 
        $_ -match "(?i)(sqlite|\.db|\.vscdb|state\.vscdb|storage\.json|ItemTable)" 
    } | Select-Object -First 10
    
    $dbOperations | ForEach-Object { Write-Host "  • $_" -ForegroundColor White }
    
    Write-Host "`n🔧 IDE检测分析:" -ForegroundColor Yellow
    $ideDetection = $strings | Where-Object { 
        $_ -match "(?i)(jetbrains|intellij|pycharm|webstorm|phpstorm|rubymine|datagrip|cursor|vscode|code\.exe)" 
    } | Select-Object -First 12
    
    $ideDetection | ForEach-Object { Write-Host "  • $_" -ForegroundColor White }
    
    Write-Host "`n🛡️ 安全机制分析:" -ForegroundColor Yellow
    $securityFeatures = $strings | Where-Object { 
        $_ -match "(?i)(backup|restore|verify|rollback|transaction|atomic|lock|permission)" 
    } | Select-Object -First 10
    
    $securityFeatures | ForEach-Object { Write-Host "  • $_" -ForegroundColor White }
    
    Write-Host "`n🌐 网络优化分析:" -ForegroundColor Yellow
    $networkFeatures = $strings | Where-Object { 
        $_ -match "(?i)(domain|speed|test|proxy|hosts|api|endpoint|timeout)" 
    } | Select-Object -First 10
    
    $networkFeatures | ForEach-Object { Write-Host "  • $_" -ForegroundColor White }
    
    Write-Host "`n📊 配置管理分析:" -ForegroundColor Yellow
    $configManagement = $strings | Where-Object { 
        $_ -match "(?i)(config|settings|properties|preferences|registry|json|xml)" 
    } | Select-Object -First 10
    
    $configManagement | ForEach-Object { Write-Host "  • $_" -ForegroundColor White }
    
} catch {
    Write-Host "分析失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n✅ 深度分析完成!" -ForegroundColor Green
