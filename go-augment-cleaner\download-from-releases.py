#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从GitHub Releases下载Augment清理工具
适用于Win11系统
"""

import requests
import os
import sys
from pathlib import Path

def download_augment_cleaner():
    """下载适合Win11的Augment清理工具"""
    
    # GitHub Releases API URL
    api_url = "https://api.github.com/repos/yuaotian/go-augment-cleaner/releases/latest"
    
    print("🔍 正在获取最新版本信息...")
    
    try:
        # 获取最新版本信息
        response = requests.get(api_url)
        response.raise_for_status()
        release_data = response.json()
        
        version = release_data['tag_name']
        print(f"📦 最新版本: {version}")
        
        # 查找适合Win11的文件
        target_filename = "augment-cleaner-windows-amd64.exe"
        download_url = None
        
        for asset in release_data['assets']:
            if asset['name'] == target_filename:
                download_url = asset['browser_download_url']
                file_size = asset['size']
                break
        
        if not download_url:
            print("❌ 未找到适合Win11的版本!")
            return False
        
        print(f"📥 开始下载: {target_filename}")
        print(f"📊 文件大小: {file_size / 1024 / 1024:.2f} MB")
        print(f"🔗 下载链接: {download_url}")
        
        # 下载文件
        response = requests.get(download_url, stream=True)
        response.raise_for_status()
        
        # 保存文件
        with open(target_filename, 'wb') as f:
            downloaded = 0
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    progress = (downloaded / file_size) * 100
                    print(f"\r⏳ 下载进度: {progress:.1f}%", end='', flush=True)
        
        print(f"\n✅ 下载完成: {target_filename}")
        
        # 验证文件
        if os.path.exists(target_filename):
            actual_size = os.path.getsize(target_filename)
            print(f"📁 文件大小: {actual_size} bytes")
            
            if actual_size == file_size:
                print("✅ 文件完整性验证通过!")
                return True
            else:
                print("❌ 文件大小不匹配!")
                return False
        else:
            print("❌ 文件下载失败!")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return False

def show_usage_info():
    """显示使用说明"""
    print("\n" + "="*50)
    print("🎯 Augment清理工具使用说明")
    print("="*50)
    print("📋 功能: 清理VS Code中的Augment插件数据")
    print("🎯 支持: Windows 11 (AMD64架构)")
    print("⚠️  注意: 使用前请关闭VS Code")
    print("\n🚀 使用方法:")
    print("1. 双击运行: augment-cleaner-windows-amd64.exe")
    print("2. 命令行运行: .\\augment-cleaner-windows-amd64.exe")
    print("3. 预览模式: .\\augment-cleaner-windows-amd64.exe -dry-run")
    print("\n📊 清理范围:")
    print("✅ VS Code Augment插件配置")
    print("✅ 缓存和临时文件")
    print("✅ 会话和设备标识数据")
    print("✅ 项目级Augment数据")
    print("✅ Windows注册表相关项")

if __name__ == "__main__":
    print("🚀 Augment清理工具下载器")
    print("=" * 30)
    
    success = download_augment_cleaner()
    
    if success:
        show_usage_info()
        print(f"\n🎉 准备就绪! 现在可以运行清理工具了!")
    else:
        print("\n❌ 下载失败，请检查网络连接或手动下载")
        print("🔗 手动下载地址: https://github.com/yuaotian/go-augment-cleaner/releases")
