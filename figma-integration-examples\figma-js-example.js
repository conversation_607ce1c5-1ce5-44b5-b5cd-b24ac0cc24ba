// Figma-JS 集成示例
// 安装: npm install figma-js

const Figma = require('figma-js');

// 初始化客户端
const client = Figma.Client({
  personalAccessToken: '*********************************************'
});

// 示例1: 获取文件信息
async function getFileInfo(fileKey) {
  try {
    const file = await client.file(fileKey);
    console.log('文件名:', file.data.name);
    console.log('页面数量:', file.data.document.children.length);
    return file.data;
  } catch (error) {
    console.error('获取文件失败:', error);
  }
}

// 示例2: 获取特定节点
async function getNodes(fileKey, nodeIds) {
  try {
    const nodes = await client.fileNodes(fileKey, { ids: nodeIds });
    console.log('节点信息:', nodes.data.nodes);
    return nodes.data.nodes;
  } catch (error) {
    console.error('获取节点失败:', error);
  }
}

// 示例3: 导出图像
async function exportImages(fileKey, nodeIds, format = 'png') {
  try {
    const images = await client.fileImages(fileKey, {
      ids: nodeIds,
      format: format,
      scale: 2
    });
    console.log('导出的图像URL:', images.data.images);
    return images.data.images;
  } catch (error) {
    console.error('导出图像失败:', error);
  }
}

// 示例4: 获取组件
async function getComponents(fileKey) {
  try {
    const components = await client.fileComponents(fileKey);
    console.log('组件列表:', components.data.meta.components);
    return components.data.meta.components;
  } catch (error) {
    console.error('获取组件失败:', error);
  }
}

// 示例5: 获取样式
async function getStyles(fileKey) {
  try {
    const styles = await client.fileStyles(fileKey);
    console.log('样式列表:', styles.data.meta.styles);
    return styles.data.meta.styles;
  } catch (error) {
    console.error('获取样式失败:', error);
  }
}

// 使用示例
async function main() {
  const fileKey = 'YOUR_FILE_KEY'; // 替换为实际的文件ID
  
  // 获取文件信息
  await getFileInfo(fileKey);
  
  // 获取特定节点
  await getNodes(fileKey, ['node-id-1', 'node-id-2']);
  
  // 导出图像
  await exportImages(fileKey, ['node-id-1'], 'svg');
  
  // 获取组件和样式
  await getComponents(fileKey);
  await getStyles(fileKey);
}

// 导出函数供其他模块使用
module.exports = {
  client,
  getFileInfo,
  getNodes,
  exportImages,
  getComponents,
  getStyles
};
