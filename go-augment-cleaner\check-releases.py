#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查GitHub Releases中的文件
"""

import requests
import json

def check_releases():
    """检查最新版本的文件列表"""
    
    api_url = "https://api.github.com/repos/yuaotian/go-augment-cleaner/releases/latest"
    
    print("🔍 正在获取最新版本信息...")
    
    try:
        response = requests.get(api_url)
        response.raise_for_status()
        release_data = response.json()
        
        version = release_data['tag_name']
        print(f"📦 最新版本: {version}")
        print(f"📅 发布时间: {release_data['published_at']}")
        print(f"📝 发布说明: {release_data['name']}")
        
        print("\n📁 可用文件列表:")
        print("-" * 50)
        
        for i, asset in enumerate(release_data['assets'], 1):
            name = asset['name']
            size = asset['size'] / 1024 / 1024  # MB
            download_count = asset['download_count']
            
            print(f"{i}. {name}")
            print(f"   📊 大小: {size:.2f} MB")
            print(f"   📥 下载次数: {download_count}")
            print(f"   🔗 下载链接: {asset['browser_download_url']}")
            print()
        
        # 查找Windows版本
        windows_files = [asset for asset in release_data['assets'] 
                        if 'windows' in asset['name'].lower()]
        
        if windows_files:
            print("🎯 适合Win11的版本:")
            for asset in windows_files:
                print(f"✅ {asset['name']}")
                return asset['browser_download_url'], asset['name'], asset['size']
        else:
            print("❌ 未找到Windows版本")
            return None, None, None
            
    except Exception as e:
        print(f"❌ 获取信息失败: {e}")
        return None, None, None

def download_file(url, filename, expected_size):
    """下载文件"""
    print(f"\n📥 开始下载: {filename}")
    
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        with open(filename, 'wb') as f:
            downloaded = 0
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    if expected_size > 0:
                        progress = (downloaded / expected_size) * 100
                        print(f"\r⏳ 下载进度: {progress:.1f}%", end='', flush=True)
        
        print(f"\n✅ 下载完成: {filename}")
        return True
        
    except Exception as e:
        print(f"\n❌ 下载失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Augment清理工具 - Releases检查器")
    print("=" * 40)
    
    url, filename, size = check_releases()
    
    if url and filename:
        print(f"\n🎯 推荐下载: {filename}")
        choice = input("\n是否现在下载? (y/n): ").lower().strip()
        
        if choice in ['y', 'yes', '是']:
            success = download_file(url, filename, size)
            if success:
                print(f"\n🎉 下载成功! 文件保存为: {filename}")
                print("\n📋 使用说明:")
                print(f"1. 双击运行: {filename}")
                print(f"2. 命令行: .\\{filename}")
                print(f"3. 预览模式: .\\{filename} -dry-run")
            else:
                print("\n❌ 下载失败")
        else:
            print(f"\n📝 手动下载链接: {url}")
    else:
        print("\n❌ 无法获取下载信息")
