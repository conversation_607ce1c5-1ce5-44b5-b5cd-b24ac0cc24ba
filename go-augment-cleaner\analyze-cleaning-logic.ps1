# Deep analysis of go-augment-cleaner cleaning logic
$FilePath = "augment-cleaner-windows-amd64-v3.2.exe"

Write-Host "=== Deep Cleaning Logic Analysis ===" -ForegroundColor Green

try {
    $bytes = [System.IO.File]::ReadAllBytes($FilePath)
    $content = [System.Text.Encoding]::ASCII.GetString($bytes)
    $strings = [regex]::Matches($content, '[\x20-\x7E]{4,}') | ForEach-Object { $_.Value }
    
    Write-Host "`nCleaning Functions Analysis:" -ForegroundColor Yellow
    $cleanFunctions = $strings | Where-Object { 
        $_ -match "(?i)(clean|clear|delete|remove|purge)" 
    } | Where-Object { $_.Length -gt 8 } | Select-Object -First 15
    
    $cleanFunctions | ForEach-Object { Write-Host "  - $_" -ForegroundColor White }
    
    Write-Host "`nPath Patterns Analysis:" -ForegroundColor Yellow
    $pathPatterns = $strings | Where-Object { 
        $_ -match "(?i)(appdata|localappdata|userprofile|\.vscode|\.config|extensions|globalStorage|workspaceStorage)" 
    } | Select-Object -First 15
    
    $pathPatterns | ForEach-Object { Write-Host "  - $_" -ForegroundColor White }
    
    Write-Host "`nDatabase Operations Analysis:" -ForegroundColor Yellow
    $dbOperations = $strings | Where-Object { 
        $_ -match "(?i)(sqlite|\.db|\.vscdb|state\.vscdb|storage\.json|ItemTable)" 
    } | Select-Object -First 10
    
    $dbOperations | ForEach-Object { Write-Host "  - $_" -ForegroundColor White }
    
    Write-Host "`nIDE Detection Analysis:" -ForegroundColor Yellow
    $ideDetection = $strings | Where-Object { 
        $_ -match "(?i)(jetbrains|intellij|pycharm|webstorm|phpstorm|rubymine|datagrip|cursor|vscode|code\.exe)" 
    } | Select-Object -First 12
    
    $ideDetection | ForEach-Object { Write-Host "  - $_" -ForegroundColor White }
    
    Write-Host "`nSecurity Features Analysis:" -ForegroundColor Yellow
    $securityFeatures = $strings | Where-Object { 
        $_ -match "(?i)(backup|restore|verify|rollback|transaction|atomic|lock|permission)" 
    } | Select-Object -First 10
    
    $securityFeatures | ForEach-Object { Write-Host "  - $_" -ForegroundColor White }
    
    Write-Host "`nNetwork Optimization Analysis:" -ForegroundColor Yellow
    $networkFeatures = $strings | Where-Object { 
        $_ -match "(?i)(domain|speed|test|proxy|hosts|api|endpoint|timeout)" 
    } | Select-Object -First 10
    
    $networkFeatures | ForEach-Object { Write-Host "  - $_" -ForegroundColor White }
    
    Write-Host "`nConfiguration Management Analysis:" -ForegroundColor Yellow
    $configManagement = $strings | Where-Object { 
        $_ -match "(?i)(config|settings|properties|preferences|registry|json|xml)" 
    } | Select-Object -First 10
    
    $configManagement | ForEach-Object { Write-Host "  - $_" -ForegroundColor White }
    
} catch {
    Write-Host "Analysis failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nDeep analysis completed!" -ForegroundColor Green
