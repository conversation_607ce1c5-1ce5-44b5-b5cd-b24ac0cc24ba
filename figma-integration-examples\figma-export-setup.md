# Figma Export Tools 集成指南

## 1. Figma Export Assets
**最强大的资源导出工具**

### 安装
```bash
npm install figma-export-assets
```

### 配置文件 (figma-export.config.js)
```javascript
module.exports = {
  // Figma 访问令牌
  figmaToken: '*********************************************',
  
  // 文件配置
  files: [
    {
      fileId: 'YOUR_FILE_ID',
      outputDir: './assets',
      
      // 导出配置
      exports: [
        {
          // 导出所有图标
          filter: (node) => node.type === 'COMPONENT' && node.name.includes('icon'),
          format: 'svg',
          suffix: '_icon'
        },
        {
          // 导出所有图片
          filter: (node) => node.type === 'RECTANGLE' && node.fills?.length > 0,
          format: 'png',
          scale: 2
        }
      ]
    }
  ],
  
  // 批量导出设置
  batchSize: 10,
  
  // 自定义命名
  nameGenerator: (node, format) => {
    return `${node.name.toLowerCase().replace(/\s+/g, '-')}.${format}`;
  }
};
```

### 使用方法
```bash
npx figma-export-assets
```

## 2. Figma SVG CLI
**专门导出SVG的命令行工具**

### 安装
```bash
npm install -g figma-svg-cli
```

### 使用
```bash
# 导出特定帧为SVG
figma-svg --file YOUR_FILE_ID --token YOUR_TOKEN --frame FRAME_ID

# 批量导出
figma-svg --file YOUR_FILE_ID --token YOUR_TOKEN --output ./svg-exports
```

## 3. Figma Theme Generator
**导出设计系统和主题**

### 安装
```bash
npm install figma-theme
```

### 配置 (.env)
```
FIGMA_TOKEN=*********************************************
FIGMA_FILE_ID=YOUR_FILE_ID
```

### 使用
```javascript
const { generateTheme } = require('figma-theme');

generateTheme({
  fileId: process.env.FIGMA_FILE_ID,
  token: process.env.FIGMA_TOKEN,
  output: './theme.json'
}).then(theme => {
  console.log('主题生成完成:', theme);
});
```

## 4. Figma to Code 工具

### Locofy Plugin
- **功能**: 将Figma设计转换为React/Vue/Angular代码
- **安装**: 在Figma插件市场搜索"Locofy"
- **特点**: AI驱动，支持响应式设计

### Anima Plugin
- **功能**: 生成HTML/CSS/React代码
- **安装**: Chrome扩展 + Figma插件
- **特点**: 实时预览，开发者友好

### Builder.io
- **功能**: 设计到代码的完整工作流
- **集成**: 直接在Figma中使用
- **特点**: 支持多种框架

## 5. 设计系统集成

### Storybook Figma Plugin
```bash
npm install @storybook/addon-design
```

### 配置
```javascript
// .storybook/main.js
module.exports = {
  addons: ['@storybook/addon-design']
};

// 在故事中使用
export default {
  title: 'Button',
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/YOUR_FILE_ID'
    }
  }
};
```

## 6. 自动化工作流

### GitHub Actions 集成
```yaml
# .github/workflows/figma-export.yml
name: Export Figma Assets

on:
  schedule:
    - cron: '0 2 * * *'  # 每天凌晨2点
  workflow_dispatch:

jobs:
  export:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '16'
          
      - name: Install dependencies
        run: npm install figma-export-assets
        
      - name: Export assets
        env:
          FIGMA_TOKEN: ${{ secrets.FIGMA_TOKEN }}
        run: npx figma-export-assets
        
      - name: Commit changes
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add .
          git commit -m "Update Figma assets" || exit 0
          git push
```

## 7. 推荐的完整工作流

1. **设计阶段**: 使用Figma进行设计
2. **导出阶段**: 使用figma-export-assets批量导出资源
3. **代码生成**: 使用Locofy或Anima生成初始代码
4. **集成阶段**: 在Storybook中展示组件
5. **自动化**: 使用GitHub Actions自动同步

## 8. 最佳实践

### 命名规范
- 组件: `Button/Primary`
- 图标: `Icon/Arrow/Right`
- 颜色: `Color/Primary/500`

### 文件组织
```
assets/
├── icons/
├── images/
├── components/
└── styles/
    ├── colors.json
    ├── typography.json
    └── spacing.json
```

### 版本控制
- 使用Figma的版本历史
- 在代码中标记对应的Figma版本
- 定期同步设计和代码
