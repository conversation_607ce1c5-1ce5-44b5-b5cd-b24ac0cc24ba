# 🔍 清理逻辑深度对比分析

## 📋 **清理机制总览**

| 清理方面 | go-augment-cleaner | AugmentCode-Free |
|---------|-------------------|------------------|
| **清理深度** | 🟢 系统级深度清理 | 🟡 应用级基础清理 |
| **清理范围** | 🟢 多IDE全覆盖 | 🟡 仅VS Code |
| **清理方式** | 🟢 文件系统+注册表 | 🟡 数据库+JSON |
| **安全机制** | 🟢 多重保护 | 🟡 基础备份 |

## 🚀 **go-augment-cleaner 清理逻辑**

### 🎯 **清理架构** (基于二进制分析)
```go
// 核心清理流程 (从二进制字符串分析得出)
func cleanAugmentFiles() {
    // 1. 多IDE检测和清理
    cleanJetbrainsAppData()    // JetBrains全系列 (确认支持)
    cleanVSCodeData()          // VS Code专门清理
    cleanCursorData()          // Cursor清理

    // 2. 系统级清理
    cleanRegistry()            // Windows注册表清理 (发现函数)
    cleanXMLFile()             // XML配置文件清理 (发现函数)
    CachesCleared()            // 缓存清理 (发现函数)

    // 3. 深度数据清理
    CleanChatOnly()            // 仅聊天记录清理 (发现函数)
    verifyCleanup()            // 清理验证 (发现函数)
    LoadAndDelete()            // 加载并删除 (发现函数)
    ConfigsCleaned()           // 配置清理完成标志 (发现函数)
}

// 发现的具体清理函数
cleanHead()                    // 清理头部数据
deleteMin()                    // 删除最小值
ClearBufs()                    // 清理缓冲区
RemoveAll()                    // 移除所有
DirsDeleted()                  // 目录删除标志
FilesDeleted()                 // 文件删除标志
AppDataCleaned()               // 应用数据清理完成
```

### 📁 **VS Code清理目标** (基于二进制分析确认)
```go
// VS Code清理逻辑 (从二进制字符串分析确认)
func cleanVSCodeData() {
    // 1. 扩展目录清理 (确认支持Extensions)
    cleanPath("%USERPROFILE%\\.vscode\\extensions\\augment*")

    // 2. 用户数据清理 (确认支持globalStorage, workspaceStorage)
    cleanPath("%APPDATA%\\Code\\User\\globalStorage\\augment*")
    cleanPath("%APPDATA%\\Code\\User\\workspaceStorage\\*\\augment*")

    // 3. 配置文件清理 (确认支持JSON配置)
    cleanJSONConfig("%APPDATA%\\Code\\User\\settings.json", "augment")
    cleanJSONConfig("%APPDATA%\\Code\\User\\keybindings.json", "augment")

    // 4. 数据库清理 (确认支持SQLite和JSON)
    cleanSQLiteDB("%APPDATA%\\Code\\User\\state.vscdb", "augment")
    cleanJSONFile("%APPDATA%\\Code\\User\\storage.json", "machineId|devDeviceId")

    // 5. 缓存清理 (确认支持CachedExtensions)
    cleanPath("%APPDATA%\\Code\\CachedExtensions\\augment*")
    cleanPath("%APPDATA%\\Code\\logs\\*augment*")
    cleanPath("%TEMP%\\vscode-*\\augment*")

    // 6. 特殊清理 (从二进制分析发现)
    cleanAugmentCodeDir()              // 专门的Augment代码目录清理
    cleanPath(".augment*")             // 隐藏的.augment文件
    cleanSessionId()                   // 会话ID清理
}
```

### 🛡️ **安全机制**
```go
// 安全保护流程
func safeClean() {
    // 1. 进程检测
    if isProcessRunning("Code.exe") {
        forceCloseProcess("Code.exe")
        waitForProcessExit(30) // 30秒超时
    }
    
    // 2. 备份机制
    createBackup(targetFiles)
    
    // 3. 验证清理
    verifyCleanupResult()
    
    // 4. 恢复选项
    if cleanupFailed {
        restoreFromBackup()
    }
}
```

## 🛠️ **AugmentCode-Free 清理逻辑**

### 🎯 **清理架构** (基于源码分析)
```python
# 核心清理流程
def main_cleaning_process():
    # 1. VS Code数据库清理
    clean_vscode_database(db_path, keyword="augment")
    
    # 2. 遥测ID修改
    modify_vscode_telemetry_ids(storage_json_path)
```

### 📊 **数据库清理逻辑**
```python
def clean_vscode_database(db_path: Path, keyword: str = "augment") -> bool:
    """VS Code SQLite数据库清理"""
    
    # 1. 安全检查
    validate_keyword(keyword)           # 关键词验证
    validate_file_path(db_path)         # 文件路径验证
    check_file_lock(db_path)           # 文件锁检查
    
    # 2. 备份数据库
    backup_path = create_backup(db_path)
    
    # 3. SQLite操作
    conn = sqlite3.connect(db_path, timeout=30.0)
    conn.execute("PRAGMA foreign_keys = ON")
    conn.execute("PRAGMA journal_mode = WAL")
    
    # 4. 查找目标条目
    like_pattern = f"%{keyword}%"
    cursor.execute(
        "SELECT key FROM ItemTable WHERE key LIKE ? COLLATE NOCASE", 
        (like_pattern,)
    )
    entries_to_delete = cursor.fetchall()
    
    # 5. 删除操作
    conn.execute("BEGIN IMMEDIATE")
    cursor.execute(
        "DELETE FROM ItemTable WHERE key LIKE ? COLLATE NOCASE", 
        (like_pattern,)
    )
    conn.commit()
    
    # 清理目标：ItemTable中包含"augment"的key-value对
```

### 🔧 **遥测ID修改逻辑**
```python
def modify_vscode_telemetry_ids(storage_json_path: Path) -> bool:
    """修改VS Code遥测标识符"""
    
    # 1. 备份JSON文件
    backup_path = create_backup(storage_json_path)
    
    # 2. 生成新ID
    new_machine_id = generate_new_machine_id()    # UUID4格式
    new_device_id = generate_new_device_id()      # UUID4格式
    
    # 3. 读取和修改JSON
    with open(storage_json_path, 'r') as f:
        data = json.load(f)
    
    # 4. 更新ID字段
    if 'machineId' in data:
        data['machineId'] = new_machine_id
        
    if 'telemetry' in data:
        if 'machineId' in data['telemetry']:
            data['telemetry']['machineId'] = new_machine_id
        if 'devDeviceId' in data['telemetry']:
            data['telemetry']['devDeviceId'] = new_device_id
    
    # 5. 写回文件
    with open(storage_json_path, 'w') as f:
        json.dump(data, f, indent=4)
    
    # 清理目标：storage.json中的machineId和devDeviceId
```

## 📊 **清理范围详细对比**

### 🎯 **清理目标对比**

| 清理项目 | go-augment-cleaner | AugmentCode-Free |
|---------|-------------------|------------------|
| **VS Code数据库** | ✅ state.vscdb全面清理 | ✅ state.vscdb基础清理 |
| **配置文件** | ✅ settings.json等多文件 | ✅ storage.json单文件 |
| **扩展目录** | ✅ 完整扩展清理 | ❌ 不涉及 |
| **缓存文件** | ✅ 多级缓存清理 | ❌ 不涉及 |
| **日志文件** | ✅ 日志清理 | ❌ 不涉及 |
| **注册表** | ✅ Windows注册表 | ❌ 不涉及 |
| **临时文件** | ✅ 系统临时文件 | ❌ 不涉及 |
| **JetBrains** | ✅ 全系列支持 | ❌ 不支持 |
| **Cursor** | ✅ 支持 | ❌ 不支持 |

### 🔍 **清理深度对比**

#### 🚀 **go-augment-cleaner** - 系统级清理
```
📁 文件系统清理
├── VS Code扩展目录 (%USERPROFILE%\.vscode\extensions\)
├── 用户数据目录 (%APPDATA%\Code\User\)
├── 工作区存储 (workspaceStorage\)
├── 全局存储 (globalStorage\)
├── 缓存目录 (CachedExtensions\)
├── 日志目录 (logs\)
└── 临时文件 (%TEMP%\vscode-*)

🗃️ 数据库清理
├── state.vscdb (完整清理)
├── storage.json (ID重置)
└── 其他SQLite数据库

🏢 注册表清理 (Windows)
├── HKEY_CURRENT_USER\Software\Microsoft\VSCode
└── 相关注册表项

🔧 多IDE支持
├── JetBrains全系列 (IntelliJ, PyCharm, WebStorm等)
├── VS Code
└── Cursor
```

#### 🛠️ **AugmentCode-Free** - 应用级清理
```
🗃️ 数据库清理
└── state.vscdb (ItemTable中的augment条目)

📄 JSON文件修改
└── storage.json (machineId, devDeviceId)

🎯 清理范围
└── 仅VS Code应用层数据
```

## 🛡️ **安全机制对比**

### 🚀 **go-augment-cleaner 安全机制**
```go
// 多重安全保护
1. 进程检测 + 强制关闭
2. 多级备份机制
3. Dry-run预览模式
4. 原子性操作
5. 清理验证
6. 自动恢复
7. 错误回滚
```

### 🛠️ **AugmentCode-Free 安全机制**
```python
# 基础安全保护
1. 文件锁检测
2. 单文件备份
3. 参数化查询 (防SQL注入)
4. 事务性操作
5. 异常处理
6. 手动恢复
```

## 🎯 **清理效果评估**

### ✅ **彻底性对比**
- **go-augment-cleaner**: 🟢 **系统级彻底清理** - 清理所有相关痕迹
- **AugmentCode-Free**: 🟡 **应用级基础清理** - 清理核心数据

### 🛡️ **安全性对比**
- **go-augment-cleaner**: 🟢 **企业级安全** - 多重保护机制
- **AugmentCode-Free**: 🟡 **基础安全** - 标准保护措施

### 🎯 **适用场景**
- **go-augment-cleaner**: 需要彻底清理、多IDE环境、企业使用
- **AugmentCode-Free**: 免费续杯、VS Code专用、轻量需求

## 🎉 **总结**

**go-augment-cleaner** 采用**系统级深度清理**策略，覆盖文件系统、注册表、多IDE支持，提供企业级安全保障。

**AugmentCode-Free** 采用**应用级精准清理**策略，专注于VS Code核心数据，轻量高效，主要服务于免费续杯需求。

**两者互补，各有优势！** 🚀
