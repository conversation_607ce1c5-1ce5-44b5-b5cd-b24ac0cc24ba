// Figma REST API 直接调用示例
// 无需额外依赖，使用原生 fetch

class FigmaAPI {
  constructor(token) {
    this.token = token;
    this.baseURL = 'https://api.figma.com/v1';
    this.headers = {
      'X-Figma-Token': token,
      'Content-Type': 'application/json'
    };
  }

  // 通用请求方法
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: this.headers,
      ...options
    };

    try {
      const response = await fetch(url, config);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('API请求失败:', error);
      throw error;
    }
  }

  // 获取文件信息
  async getFile(fileKey, options = {}) {
    const params = new URLSearchParams(options);
    const endpoint = `/files/${fileKey}?${params}`;
    return await this.request(endpoint);
  }

  // 获取特定节点
  async getNodes(fileKey, nodeIds) {
    const ids = Array.isArray(nodeIds) ? nodeIds.join(',') : nodeIds;
    const endpoint = `/files/${fileKey}/nodes?ids=${ids}`;
    return await this.request(endpoint);
  }

  // 导出图像
  async exportImages(fileKey, nodeIds, options = {}) {
    const ids = Array.isArray(nodeIds) ? nodeIds.join(',') : nodeIds;
    const params = new URLSearchParams({
      ids,
      format: options.format || 'png',
      scale: options.scale || '1',
      ...options
    });
    const endpoint = `/images/${fileKey}?${params}`;
    return await this.request(endpoint);
  }

  // 获取组件
  async getComponents(fileKey) {
    const endpoint = `/files/${fileKey}/components`;
    return await this.request(endpoint);
  }

  // 获取样式
  async getStyles(fileKey) {
    const endpoint = `/files/${fileKey}/styles`;
    return await this.request(endpoint);
  }

  // 获取评论
  async getComments(fileKey) {
    const endpoint = `/files/${fileKey}/comments`;
    return await this.request(endpoint);
  }

  // 添加评论
  async addComment(fileKey, message, clientMeta) {
    const endpoint = `/files/${fileKey}/comments`;
    return await this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify({
        message,
        client_meta: clientMeta
      })
    });
  }

  // 获取团队项目
  async getTeamProjects(teamId) {
    const endpoint = `/teams/${teamId}/projects`;
    return await this.request(endpoint);
  }

  // 获取项目文件
  async getProjectFiles(projectId) {
    const endpoint = `/projects/${projectId}/files`;
    return await this.request(endpoint);
  }
}

// 使用示例
async function example() {
  const figma = new FigmaAPI('*********************************************');
  
  try {
    // 使用一个公开的Figma社区文件进行测试
    const fileKey = 'HhYJ2hXZFaakQwQyDXkdRf'; // Figma社区示例文件
    
    // 获取文件信息
    console.log('正在获取文件信息...');
    const fileInfo = await figma.getFile(fileKey);
    console.log('文件名:', fileInfo.name);
    console.log('最后修改:', fileInfo.lastModified);
    
    // 获取第一个页面的所有子节点
    const firstPage = fileInfo.document.children[0];
    console.log('第一个页面:', firstPage.name);
    
    if (firstPage.children && firstPage.children.length > 0) {
      const firstNode = firstPage.children[0];
      console.log('第一个节点ID:', firstNode.id);
      
      // 获取特定节点详情
      const nodeDetails = await figma.getNodes(fileKey, [firstNode.id]);
      console.log('节点详情:', nodeDetails.nodes[firstNode.id]);
      
      // 导出为PNG图像
      const images = await figma.exportImages(fileKey, [firstNode.id], {
        format: 'png',
        scale: '2'
      });
      console.log('导出的图像URL:', images.images);
    }
    
    // 获取组件
    const components = await figma.getComponents(fileKey);
    console.log('组件数量:', Object.keys(components.meta.components).length);
    
  } catch (error) {
    console.error('示例执行失败:', error);
  }
}

// 导出类和示例函数
module.exports = { FigmaAPI, example };

// 如果直接运行此文件，执行示例
if (require.main === module) {
  example();
}
