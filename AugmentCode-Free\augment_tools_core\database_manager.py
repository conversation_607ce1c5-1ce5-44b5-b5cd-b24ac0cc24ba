import sqlite3
import shutil
from pathlib import Path
import logging
import os
from .common_utils import (
    print_info, print_success, print_warning, print_error,
    create_backup, validate_keyword, validate_file_path
)

# Configure basic logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def clean_vscode_database(db_path: Path, keyword: str = "augment") -> bool:
    """
    Cleans the VS Code SQLite database by removing entries containing a specific keyword.
    Enhanced with security validations and improved error handling.

    Args:
        db_path: Path to the VS Code state.vscdb SQLite database.
        keyword: The keyword to search for in the 'key' column of 'ItemTable'.
                 Entries containing this keyword will be removed.

    Returns:
        True if the database was cleaned successfully or if no cleaning was needed,
        False otherwise.
    """
    # Enhanced security validation
    if not validate_keyword(keyword):
        print_error(f"Invalid keyword: '{keyword}'. Keywords must be 1-100 characters and contain only alphanumeric characters, dots, hyphens, and underscores.")
        return False

    if not validate_file_path(db_path):
        print_error(f"Database file validation failed: {db_path}")
        return False

    print_info(f"Attempting to clean VS Code database: {db_path}")
    print_info(f"Target keyword for cleaning: '{keyword}' (validated)")

    # Additional file checks
    try:
        file_size = db_path.stat().st_size
        print_info(f"Database file size: {file_size / 1024:.1f} KB")

        # Check if file is locked (VS Code running)
        try:
            with open(db_path, 'r+b') as test_file:
                pass  # Just test if we can open for writing
        except PermissionError:
            print_error("Database file is locked. Please ensure VS Code is completely closed.")
            return False

    except Exception as e:
        print_error(f"Error checking database file: {e}")
        return False

    backup_path = None
    try:
        # 1. Create a backup
        print_info("Backing up database...")
        backup_path = create_backup(db_path)
        if not backup_path:
            # Error message already printed by create_backup
            return False
        print_success(f"Database backed up successfully to: {backup_path}")

        # 2. Connect to the SQLite database with enhanced security
        print_info(f"Connecting to database: {db_path}")

        # Use secure connection settings
        conn = sqlite3.connect(
            db_path,
            timeout=30.0,  # 30 second timeout
            isolation_level='DEFERRED',  # Better concurrency control
            check_same_thread=False  # Allow multi-threading if needed
        )

        # Enable foreign key constraints and other security features
        conn.execute("PRAGMA foreign_keys = ON")
        conn.execute("PRAGMA journal_mode = WAL")  # Better concurrency and crash recovery

        cursor = conn.cursor()
        print_success("Successfully connected to the database with security settings.")

        # Verify database structure before proceeding
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='ItemTable'")
        if not cursor.fetchone():
            print_error("ItemTable not found in database. This may not be a valid VS Code database.")
            conn.close()
            return False

        # Verify table structure
        cursor.execute("PRAGMA table_info(ItemTable)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        if 'key' not in column_names or 'value' not in column_names:
            print_error("ItemTable does not have expected structure (key, value columns).")
            conn.close()
            return False

        # 3. Find and count entries to be deleted with parameterized query
        print_info(f"Searching for entries with keyword '{keyword}'...")

        # Use parameterized query to prevent SQL injection
        # The LIKE pattern is constructed safely since keyword is validated
        like_pattern = f"%{keyword}%"
        cursor.execute("SELECT key FROM ItemTable WHERE key LIKE ? COLLATE NOCASE", (like_pattern,))
        entries_to_delete = cursor.fetchall()
        
        num_entries_to_delete = len(entries_to_delete)

        if num_entries_to_delete == 0:
            print_success(f"No entries found with keyword '{keyword}'. Database is already clean.")
            conn.close()
            return True

        print_warning(f"Found {num_entries_to_delete} entries containing '{keyword}':")
        for i, entry in enumerate(entries_to_delete):
            print_info(f"  {i+1}. {entry[0]}")

        # Confirm before deletion (optional, but good for safety)
        # confirm = input(f"Proceed with deleting these {num_entries_to_delete} entries? (yes/no): ").strip().lower()
        # if confirm != 'yes':
        #     print_info("Deletion cancelled by user.")
        #     conn.close()
        #     return True # Or False, depending on desired behavior for cancellation

        # 4. Delete the entries with transaction safety
        print_info(f"Deleting {num_entries_to_delete} entries...")

        try:
            # Begin explicit transaction for atomic operation
            conn.execute("BEGIN IMMEDIATE")

            # Use the same parameterized query for consistency
            cursor.execute("DELETE FROM ItemTable WHERE key LIKE ? COLLATE NOCASE", (like_pattern,))
            deleted_rows = cursor.rowcount

            # Verify deletion count matches expectation
            if deleted_rows == num_entries_to_delete:
                conn.commit()
                print_success(f"Successfully deleted {deleted_rows} entries containing '{keyword}'.")
            elif deleted_rows > 0:
                # Partial deletion - still commit but warn user
                conn.commit()
                print_warning(f"Attempted to delete {num_entries_to_delete} entries, but {deleted_rows} were actually deleted.")
                print_success(f"Partial success: {deleted_rows} entries deleted.")
            else:
                # No rows deleted despite being found - rollback
                conn.rollback()
                print_error("No entries were deleted despite being found. Rolling back transaction.")
                raise sqlite3.Error("Deletion failed: No rows affected despite entries being found.")

        except sqlite3.Error as db_error:
            # Rollback on any database error
            try:
                conn.rollback()
                print_warning("Transaction rolled back due to error.")
            except:
                pass  # Rollback might fail if connection is broken
            raise db_error


        # 5. Close the connection
        conn.close()
        print_success("Database cleaning process completed.")
        return True

    except sqlite3.Error as e:
        print_error(f"SQLite error occurred: {e}")
        if backup_path and backup_path.exists():
            print_warning(f"Attempting to restore database from backup: {backup_path}")
            try:
                shutil.copy2(backup_path, db_path)
                print_success("Database successfully restored from backup.")
            except Exception as restore_e:
                print_error(f"Failed to restore database from backup: {restore_e}")
                print_error(f"The original database {db_path} might be corrupted or in an inconsistent state.")
                print_error(f"The backup is available at: {backup_path}")
        return False
    except Exception as e:
        print_error(f"An unexpected error occurred: {e}")
        # Similar backup restoration logic could be added here if deemed necessary
        # For now, focusing on SQLite errors for restoration.
        return False

if __name__ == '__main__':
    # This is for direct testing of this module, not part of the CLI.
    print_info("Running database_manager.py directly for testing.")
    
    # --- IMPORTANT ---
    # For testing, you MUST provide a valid path to a VS Code state.vscdb file.
    # It's highly recommended to use a COPY of your actual state.vscdb for testing
    # to avoid accidental data loss in your VS Code setup.
    #
    # Example:
    # test_db_path = Path.home() / "AppData" / "Roaming" / "Code" / "User" / "globalStorage" / "state.vscdb" # Windows
    # test_db_path = Path.home() / ".config" / "Code" / "User" / "globalStorage" / "state.vscdb" # Linux
    # test_db_path = Path.home() / "Library" / "Application Support" / "Code" / "User" / "globalStorage" / "state.vscdb" # macOS

    # Create a dummy database for testing if you don't want to use a real one
    dummy_db_path = Path("./test_state.vscdb")
    
    # Create a copy for testing to avoid modifying the original dummy
    test_dummy_db_path = Path("./test_state_copy.vscdb")

    if dummy_db_path.exists():
        dummy_db_path.unlink() # Delete if exists from previous run

    conn_test = sqlite3.connect(dummy_db_path)
    cursor_test = conn_test.cursor()
    cursor_test.execute("CREATE TABLE IF NOT EXISTS ItemTable (key TEXT PRIMARY KEY, value BLOB)")
    test_data = [
        ("storage.testKey1", b"testValue1"),
        ("augment.testKey2", b"testValue2"),
        ("another.augment.key", b"testValue3"),
        ("noKeywordHere", b"testValue4"),
        ("prefix.augment", b"testValue5"),
    ]
    cursor_test.executemany("INSERT OR IGNORE INTO ItemTable VALUES (?, ?)", test_data)
    conn_test.commit()
    conn_test.close()
    print_success(f"Created dummy database at {dummy_db_path} with test data.")

    # Make a copy to test on
    shutil.copy2(dummy_db_path, test_dummy_db_path)
    print_info(f"Copied dummy database to {test_dummy_db_path} for cleaning test.")

    print_info("\n--- Test Case 1: Cleaning with default keyword 'augment' ---")
    if clean_vscode_database(test_dummy_db_path, keyword="augment"):
        print_success("Test Case 1: Cleaning successful.")
    else:
        print_error("Test Case 1: Cleaning failed.")

    # Verify content after cleaning
    conn_verify = sqlite3.connect(test_dummy_db_path)
    cursor_verify = conn_verify.cursor()
    cursor_verify.execute("SELECT key FROM ItemTable")
    remaining_keys = [row[0] for row in cursor_verify.fetchall()]
    print_info(f"Remaining keys in {test_dummy_db_path}: {remaining_keys}")
    expected_keys = ["storage.testKey1", "noKeywordHere"]
    assert all(k in remaining_keys for k in expected_keys) and len(remaining_keys) == len(expected_keys), \
        f"Test Case 1 Verification Failed! Expected {expected_keys}, got {remaining_keys}"
    print_success("Test Case 1: Verification successful.")
    conn_verify.close()


    print_info("\n--- Test Case 2: Cleaning with a keyword that finds nothing ('nonexistent') ---")
    # Re-copy the original dummy db for a fresh test
    shutil.copy2(dummy_db_path, test_dummy_db_path)
    if clean_vscode_database(test_dummy_db_path, keyword="nonexistent"):
        print_success("Test Case 2: Cleaning reported success (as expected, no items to clean).")
    else:
        print_error("Test Case 2: Cleaning failed.")
    
    conn_verify_2 = sqlite3.connect(test_dummy_db_path)
    cursor_verify_2 = conn_verify_2.cursor()
    cursor_verify_2.execute("SELECT COUNT(*) FROM ItemTable")
    count_after_no_keyword = cursor_verify_2.fetchone()[0]
    assert count_after_no_keyword == len(test_data), \
        f"Test Case 2 Verification Failed! Expected {len(test_data)} items, got {count_after_no_keyword}"
    print_success("Test Case 2: Verification successful (no items were deleted).")
    conn_verify_2.close()

    print_info("\n--- Test Case 3: Database file does not exist ---")
    non_existent_db_path = Path("./non_existent_db.vscdb")
    if non_existent_db_path.exists():
        non_existent_db_path.unlink() # Ensure it doesn't exist
        
    if not clean_vscode_database(non_existent_db_path):
        print_success("Test Case 3: Handled non-existent database file correctly (returned False).")
    else:
        print_error("Test Case 3: Failed to handle non-existent database file.")

    # Clean up dummy files
    if dummy_db_path.exists():
        dummy_db_path.unlink()
    if test_dummy_db_path.exists():
        test_dummy_db_path.unlink()
    print_info("\nCleaned up dummy database files.")
    print_success("All database_manager tests completed.")
