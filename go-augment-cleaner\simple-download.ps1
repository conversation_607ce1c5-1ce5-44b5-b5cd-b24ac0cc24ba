# 简单下载脚本
$url = "https://github.com/yuaotian/go-augment-cleaner/releases/download/v3.2/augment-cleaner-windows-amd64.exe"
$output = "augment-cleaner-windows-amd64.exe"

Write-Host "正在下载Augment清理工具..."

$webClient = New-Object System.Net.WebClient
$webClient.DownloadFile($url, $output)

Write-Host "下载完成!"
if (Test-Path $output) {
    Write-Host "文件已保存: $output"
    Write-Host "文件大小: $((Get-Item $output).Length) bytes"
} else {
    Write-Host "下载失败!"
}
