# Network optimization features analysis
$FilePath = "augment-cleaner-windows-amd64-v3.2.exe"

Write-Host "=== Network Optimization Features Analysis ===" -ForegroundColor Green

try {
    $bytes = [System.IO.File]::ReadAllBytes($FilePath)
    $content = [System.Text.Encoding]::ASCII.GetString($bytes)
    $strings = [regex]::Matches($content, '[\x20-\x7E]{4,}') | ForEach-Object { $_.Value }
    
    Write-Host "`nDomain and API Analysis:" -ForegroundColor Yellow
    $domainPatterns = $strings | Where-Object { 
        $_ -match "(?i)(augmentcode|api\.|domain|host|dns|speed|test)" 
    } | Select-Object -First 20
    
    $domainPatterns | ForEach-Object { Write-Host "  - $_" -ForegroundColor White }
    
    Write-Host "`nHosts File Operations:" -ForegroundColor Yellow
    $hostsOperations = $strings | Where-Object { 
        $_ -match "(?i)(hosts|etc/hosts|drivers.*etc|hosts.*file)" 
    } | Select-Object -First 10
    
    $hostsOperations | ForEach-Object { Write-Host "  - $_" -ForegroundColor White }
    
    Write-Host "`nProxy Configuration:" -ForegroundColor Yellow
    $proxyFeatures = $strings | Where-Object { 
        $_ -match "(?i)(proxy|socks|http_proxy|https_proxy|no_proxy)" 
    } | Select-Object -First 15
    
    $proxyFeatures | ForEach-Object { Write-Host "  - $_" -ForegroundColor White }
    
    Write-Host "`nNetwork Testing Functions:" -ForegroundColor Yellow
    $networkFunctions = $strings | Where-Object { 
        $_ -match "(?i)(ping|connect|timeout|latency|speed|test|measure)" 
    } | Select-Object -First 15
    
    $networkFunctions | ForEach-Object { Write-Host "  - $_" -ForegroundColor White }
    
    Write-Host "`nIP and DNS Operations:" -ForegroundColor Yellow
    $ipDnsOperations = $strings | Where-Object { 
        $_ -match "(?i)(resolve|lookup|getaddr|ip|dns|nameserver)" 
    } | Select-Object -First 12
    
    $ipDnsOperations | ForEach-Object { Write-Host "  - $_" -ForegroundColor White }
    
    Write-Host "`nDry-run and Preview Features:" -ForegroundColor Yellow
    $dryrunFeatures = $strings | Where-Object { 
        $_ -match "(?i)(dry.?run|preview|simulate|test.?mode|no.?change)" 
    } | Select-Object -First 8
    
    $dryrunFeatures | ForEach-Object { Write-Host "  - $_" -ForegroundColor White }
    
} catch {
    Write-Host "Analysis failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nNetwork features analysis completed!" -ForegroundColor Green
