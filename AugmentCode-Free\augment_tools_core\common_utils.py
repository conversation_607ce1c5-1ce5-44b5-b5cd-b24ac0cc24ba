"""
Common utility functions for Augment Tools Core
"""
import os
import platform
import shutil
import uuid
import re
import subprocess
import time
from pathlib import Path
from typing import Dict, Union # Added Union for type hinting
# Configuration constants (moved from config.py to avoid import issues)
KEYWORD_MIN_LENGTH = 1
KEYWORD_MAX_LENGTH = 100
KEYWORD_PATTERN = r'^[a-zA-Z0-9._-]+$'
MAX_FILE_SIZE_BYTES = 100 * 1024 * 1024  # 100MB
PROCESS_CLOSE_TIMEOUT = 15
GRACEFUL_SHUTDOWN_WAIT = 2

try:
    from colorama import init, Fore, Style
    init(autoreset=True)  # Initialize colorama for Windows support and auto-reset styles
    IS_COLORAMA_AVAILABLE = True
except ImportError:
    IS_COLORAMA_AVAILABLE = False

# --- Console Message Functions ---
def print_message(prefix: str, message: str, color_code: str = "") -> None:
    """Helper function to print messages with optional color."""
    if IS_COLORAMA_AVAILABLE and color_code:
        print(f"{color_code}{prefix}{Style.RESET_ALL} {message}")
    else:
        print(f"{prefix} {message}")

def print_info(message: str) -> None:
    """Prints an informational message (blue if colorama is available)."""
    prefix = "[INFO]"
    color = Fore.BLUE if IS_COLORAMA_AVAILABLE else ""
    print_message(prefix, message, color)

def print_success(message: str) -> None:
    """Prints a success message (green if colorama is available)."""
    prefix = "[SUCCESS]"
    color = Fore.GREEN if IS_COLORAMA_AVAILABLE else ""
    print_message(prefix, message, color)

def print_warning(message: str) -> None:
    """Prints a warning message (yellow if colorama is available)."""
    prefix = "[WARNING]"
    color = Fore.YELLOW if IS_COLORAMA_AVAILABLE else ""
    print_message(prefix, message, color)

def print_error(message: str) -> None:
    """Prints an error message (red if colorama is available)."""
    prefix = "[ERROR]"
    color = Fore.RED if IS_COLORAMA_AVAILABLE else ""
    print_message(prefix, message, color)

# --- VS Code Path Functions ---
def get_os_specific_vscode_paths() -> Dict[str, Path]:
    """
    Determines and returns OS-specific paths for VS Code configuration files.

    This function has been optimized for the current system:
    Windows with APPDATA at C:\\Users\\<USER>\\AppData\\Roaming

    Returns:
        A dictionary containing 'state_db' and 'storage_json' paths.
    Raises:
        SystemExit: If the OS is not supported or paths are not accessible.
    """
    system = platform.system()
    paths: Dict[str, Path] = {}

    try:
        if system == "Windows":
            # Optimized for current system - verified path
            appdata = os.environ.get("APPDATA")
            if not appdata:
                print_error("APPDATA environment variable not found. Cannot locate VS Code data.")
                raise SystemExit(1)

            # Validate APPDATA path exists and is accessible
            appdata_path = Path(appdata)
            if not appdata_path.exists() or not appdata_path.is_dir():
                print_error(f"APPDATA path does not exist or is not accessible: {appdata_path}")
                raise SystemExit(1)

            base_dir = appdata_path / "Code" / "User"

        elif system == "Darwin":  # macOS
            base_dir = Path.home() / "Library" / "Application Support" / "Code" / "User"
        elif system == "Linux":
            base_dir = Path.home() / ".config" / "Code" / "User"
        else:
            print_error(f"Unsupported operating system: {system}")
            raise SystemExit(1)

        # Validate base directory exists
        if not base_dir.exists():
            print_error(f"VS Code user directory not found: {base_dir}")
            print_info("Please ensure VS Code is installed and has been run at least once.")
            raise SystemExit(1)

        global_storage = base_dir / "globalStorage"
        if not global_storage.exists():
            print_error(f"VS Code globalStorage directory not found: {global_storage}")
            raise SystemExit(1)

        paths["state_db"] = global_storage / "state.vscdb"
        paths["storage_json"] = global_storage / "storage.json"

        # Validate file permissions
        for key, path in paths.items():
            if path.exists():
                if not os.access(path, os.R_OK):
                    print_error(f"No read permission for {key}: {path}")
                    raise SystemExit(1)
                if not os.access(path, os.W_OK):
                    print_error(f"No write permission for {key}: {path}")
                    raise SystemExit(1)

        return paths

    except Exception as e:
        print_error(f"Failed to determine VS Code paths: {e}")
        raise SystemExit(1)

# --- File Backup Function ---
def create_backup(file_path: Union[str, Path]) -> Union[Path, None]:
    """
    Creates a backup of the given file.

    Args:
        file_path: The path to the file to be backed up.

    Returns:
        The path to the backup file if successful, None otherwise.
    """
    original_path = Path(file_path)
    if not original_path.exists():
        print_error(f"File not found for backup: {original_path}")
        return None

    backup_path = original_path.with_suffix(original_path.suffix + ".backup")
    try:
        shutil.copy2(original_path, backup_path)
        print_success(f"Backup created successfully at: {backup_path}")
        return backup_path
    except Exception as e:
        print_error(f"Failed to create backup for {original_path}: {e}")
        return None

# --- ID Generation Functions ---
def generate_new_machine_id() -> str:
    """Generates a new 64-character hexadecimal string for machineId."""
    return uuid.uuid4().hex + uuid.uuid4().hex

def generate_new_device_id() -> str:
    """Generates a new standard UUID v4 string for devDeviceId."""
    return str(uuid.uuid4())

# --- Input Validation Functions ---
def validate_keyword(keyword: str) -> bool:
    """
    Validates the keyword for database cleaning to prevent SQL injection.
    Uses configuration values for validation rules.

    Args:
        keyword: The keyword to validate

    Returns:
        True if keyword is safe, False otherwise
    """
    if not keyword or not isinstance(keyword, str):
        return False

    # Remove leading/trailing whitespace
    keyword = keyword.strip()

    # Check length using configuration
    if len(keyword) < KEYWORD_MIN_LENGTH or len(keyword) > KEYWORD_MAX_LENGTH:
        return False

    # Use pattern from configuration to prevent SQL injection
    if not re.match(KEYWORD_PATTERN, keyword):
        return False

    return True

def validate_file_path(file_path: Union[str, Path]) -> bool:
    """
    Validates a file path for security and accessibility.

    Args:
        file_path: The file path to validate

    Returns:
        True if path is safe and accessible, False otherwise
    """
    try:
        path = Path(file_path)

        # Check if path exists
        if not path.exists():
            return False

        # Check if it's actually a file
        if not path.is_file():
            return False

        # Check file size using configuration
        file_size = path.stat().st_size
        if file_size > MAX_FILE_SIZE_BYTES:
            max_size_mb = MAX_FILE_SIZE_BYTES / (1024*1024)
            current_size_mb = file_size / (1024*1024)
            print_warning(f"File too large: {current_size_mb:.1f}MB (max: {max_size_mb}MB)")
            return False

        # Check read/write permissions
        if not os.access(path, os.R_OK):
            print_error(f"No read permission for file: {path}")
            return False

        if not os.access(path, os.W_OK):
            print_error(f"No write permission for file: {path}")
            return False

        return True

    except Exception as e:
        print_error(f"Error validating file path: {e}")
        return False

def safe_close_processes(process_names: list, timeout: int = None) -> bool:
    """
    Safely close processes with timeout and confirmation.
    Uses configuration values for timeout settings.

    Args:
        process_names: List of process names to close
        timeout: Timeout in seconds for graceful shutdown (uses config default if None)

    Returns:
        True if processes were closed successfully, False otherwise
    """
    if timeout is None:
        timeout = PROCESS_CLOSE_TIMEOUT

    system = platform.system().lower()
    closed_any = False

    try:
        for process_name in process_names:
            if not isinstance(process_name, str) or not process_name.strip():
                continue

            process_name = process_name.strip()

            # First try graceful shutdown
            print_info(f"Attempting graceful shutdown of {process_name}...")

            if system == "windows":
                # Try graceful close first
                result = subprocess.run(['taskkill', '/IM', process_name],
                                      capture_output=True, text=True, timeout=timeout)
                if result.returncode == 0:
                    closed_any = True
                    print_success(f"Gracefully closed {process_name}")
                    continue

                # If graceful close failed, wait a bit then force close
                time.sleep(GRACEFUL_SHUTDOWN_WAIT)
                print_warning(f"Graceful close failed for {process_name}, using force close...")
                result = subprocess.run(['taskkill', '/F', '/IM', process_name],
                                      capture_output=True, text=True, timeout=timeout)
                if result.returncode == 0:
                    closed_any = True
                    print_info(f"Force closed {process_name}")

            elif system == "darwin":  # macOS
                result = subprocess.run(['pkill', '-TERM', process_name],
                                      capture_output=True, text=True, timeout=timeout)
                if result.returncode == 0:
                    closed_any = True
                    time.sleep(GRACEFUL_SHUTDOWN_WAIT)  # Give time for graceful shutdown
                    # Check if still running, then force kill
                    check_result = subprocess.run(['pgrep', process_name],
                                                capture_output=True, text=True)
                    if check_result.returncode == 0:
                        subprocess.run(['pkill', '-KILL', process_name],
                                     capture_output=True, text=True)

            else:  # Linux
                result = subprocess.run(['pkill', '-TERM', process_name],
                                      capture_output=True, text=True, timeout=timeout)
                if result.returncode == 0:
                    closed_any = True
                    time.sleep(GRACEFUL_SHUTDOWN_WAIT)  # Give time for graceful shutdown
                    # Check if still running, then force kill
                    check_result = subprocess.run(['pgrep', process_name],
                                                capture_output=True, text=True)
                    if check_result.returncode == 0:
                        subprocess.run(['pkill', '-KILL', process_name],
                                     capture_output=True, text=True)

        return closed_any

    except subprocess.TimeoutExpired:
        print_error(f"Timeout while trying to close processes")
        return False
    except Exception as e:
        print_error(f"Error closing processes: {e}")
        return False

if __name__ == '__main__':
    # Quick test for the utility functions
    print_info("Testing common_utils.py...")

    print_info("Displaying detected VS Code paths:")
    try:
        vscode_paths = get_os_specific_vscode_paths()
        print_success(f"  State DB: {vscode_paths['state_db']}")
        print_success(f"  Storage JSON: {vscode_paths['storage_json']}")
    except SystemExit:
        print_warning("Could not retrieve VS Code paths on this system (expected if run in isolated env).")


    print_info("Generating sample IDs:")
    machine_id = generate_new_machine_id()
    device_id = generate_new_device_id()
    print_success(f"  Generated Machine ID: {machine_id} (Length: {len(machine_id)})")
    print_success(f"  Generated Device ID: {device_id}")

    # To test backup, you'd need a dummy file.
    # Example:
    # test_file = Path("dummy_test_file.txt")
    # with open(test_file, "w") as f:
    #     f.write("This is a test file for backup.")
    # backup_result = create_backup(test_file)
    # if backup_result:
    #     print_info(f"Backup test successful. Backup at: {backup_result}")
    #     if backup_result.exists():
    #         backup_result.unlink() # Clean up backup
    # if test_file.exists():
    #     test_file.unlink() # Clean up test file

    print_info("Testing message types:")
    print_success("This is a success message.")
    print_warning("This is a warning message.")
    print_error("This is an error message.")
    print_info("common_utils.py tests complete.")