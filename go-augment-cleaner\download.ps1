# 下载Augment清理工具
$url = "https://github.com/yuaotian/go-augment-cleaner/releases/download/v3.2/augment-cleaner-windows-amd64.exe"
$output = "augment-cleaner-windows-amd64.exe"

Write-Host "正在下载Augment清理工具..."
Write-Host "URL: $url"

try {
    # 使用.NET WebClient下载
    $webClient = New-Object System.Net.WebClient
    $webClient.Headers.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
    $webClient.DownloadFile($url, $output)

    Write-Host "下载完成: $output"
    Write-Host "文件大小: $((Get-Item $output).Length) bytes"
} catch {
    Write-Host "下载失败: $($_.Exception.Message)"

    # 尝试使用Invoke-WebRequest
    try {
        Write-Host "尝试备用下载方法..."
        $userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        Invoke-WebRequest -Uri $url -OutFile $output -UserAgent $userAgent
        Write-Host "备用方法下载成功!"
    } catch {
        Write-Host "所有下载方法都失败了: $($_.Exception.Message)"
    }
}
